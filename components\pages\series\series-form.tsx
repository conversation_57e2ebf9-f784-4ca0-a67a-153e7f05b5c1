"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Form,
   FormControl,
   FormField,
   FormItem,
   FormLabel,
   FormMessage,
} from "@/components/ui/form";
import { FormWrapper } from "@/components/ui/form-wrapper";
import { Input } from "@/components/ui/input";
import MultipleSelector from "@/components/ui/multiselect";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { ACCEPTED_IMAGE_TYPES, MAX_FILE_SIZE } from "@/lib/constants";
import { createSeries, updateSeries } from "@/lib/firebase/series/actions";
import { getSeriesById } from "@/lib/firebase/series/service";
import { formatBytes } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useRef, useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useEpisodes } from "./context/episodesProvider";
import EpisodesTable from "./episodes-table";
import { genres } from "./genres";

export const formSchema = z.object({
   title: z.string().min(3, {
      message: "Title must be at least 3 characters.",
   }),
   year: z
      .string()
      .refine(
         (year) =>
            Number(year) >= 1900 && Number(year) <= new Date().getFullYear(),
         {
            message: `Year must be a 4 digit number between 1900 and ${new Date().getFullYear()}.`,
         },
      ),
   description: z.string().min(10, {
      message: "Description must be at least 10 characters.",
   }),
   category: z.enum(["show", "series"], {
      required_error: "Please select a category.",
   }),
   poster: z.union([
      z
         .instanceof(File, {
            message: "Please select an image file.",
         })
         .refine((file) => file.size <= MAX_FILE_SIZE, {
            message: `The image is too large. Please choose an image smaller than ${formatBytes(MAX_FILE_SIZE)}.`,
         })
         .refine((file) => ACCEPTED_IMAGE_TYPES.includes(file.type), {
            message: "Please upload a valid image file (JPEG, PNG, or WebP).",
         }),
      z.string().url({
         message: "Please provide a valid URL for the image.",
      }),
   ]),
   trailerLink: z.string().url({
      message: "Please provide a valid URL for the trailer.",
   }),
   genres: z
      .array(
         z.object({
            value: z.string(),
            label: z.string(),
         }),
      )
      .min(1, {
         message: "Please select at least one genre.",
      }),
});

export type SeriesFormType = z.infer<typeof formSchema>;

function SeriesForm() {
   const searchParams = useSearchParams();
   const router = useRouter();
   const id = searchParams.get("id");

   const { episodes, populateEpisodes } = useEpisodes();

   const populateEpisodesRef = useRef(populateEpisodes);

   const [pending, startTransition] = useTransition();
   const [error, setError] = useState({ message: "" });
   const [image, setImage] = useState<string | Blob>("");
   const [dataLoaded, setDataLoaded] = useState<boolean | undefined>(
      id ? false : undefined,
   );

   const form = useForm<z.infer<typeof formSchema>>({
      resolver: zodResolver(formSchema),
      defaultValues: {
         title: "",
         year: "",
         description: "",
         trailerLink: "",
         genres: [],
         poster: undefined,
      },
   });

   async function onSubmit(values: z.infer<typeof formSchema>) {
      startTransition(async () => {
         const result = id
            ? await updateSeries(values, id, episodes)
            : await createSeries(values, episodes);

         if (!result.success) {
            setError(result);
            toast.error(result.message);
            return;
         }

         toast.success(result.message);

         form.reset();
         router.push("/series");
      });
   }

   useEffect(() => {
      async function getSeriesData() {
         if (!id) return;

         const data = await getSeriesById(id);

         if (data) {
            form.setValue("title", data.title);
            form.setValue("year", data.year.toString());
            form.setValue("description", data.description);
            form.setValue("category", data.category);
            form.setValue("trailerLink", data.trailerLink);
            form.setValue(
               "genres",
               data.genres.map((genre) => ({ value: genre, label: genre })),
            );
            form.setValue("poster", data.poster);
            setImage(data.poster);
            populateEpisodesRef.current(data.episodes);
            setDataLoaded(true);
         } else {
            setDataLoaded(false);
         }
      }

      getSeriesData();
   }, [id, form]);

   return (
      <FormWrapper isSubmitting={pending} isDataLoaded={dataLoaded}>
         <h1 className="pb-4 text-2xl font-semibold">
            {id ? "Update" : "Add New"} Series
         </h1>

         <div className="space-y-4">
            <Form {...form}>
               <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
               >
                  <FormField
                     control={form.control}
                     name="title"
                     render={({ field }) => (
                        <FormItem>
                           <FormLabel>Series Title</FormLabel>
                           <FormControl>
                              <Input placeholder="Series Title" {...field} />
                           </FormControl>
                           <FormMessage />
                        </FormItem>
                     )}
                  />
                  <FormField
                     control={form.control}
                     name="year"
                     render={({ field }) => (
                        <FormItem>
                           <FormLabel>Release Year</FormLabel>
                           <FormControl>
                              <Input
                                 type="number"
                                 placeholder="Release Year"
                                 {...field}
                              />
                           </FormControl>
                           <FormMessage />
                        </FormItem>
                     )}
                  />
                  <FormField
                     control={form.control}
                     name="description"
                     render={({ field }) => (
                        <FormItem>
                           <FormLabel>Series Description</FormLabel>
                           <FormControl>
                              <Input
                                 placeholder="Series Description"
                                 {...field}
                              />
                           </FormControl>
                           <FormMessage />
                        </FormItem>
                     )}
                  />
                  <FormField
                     control={form.control}
                     name="category"
                     render={({ field }) => (
                        <FormItem>
                           <FormLabel>Category</FormLabel>
                           <FormControl>
                              <Select
                                 {...field}
                                 onValueChange={(value) =>
                                    field.onChange(value)
                                 }
                              >
                                 <SelectTrigger className="w-fit whitespace-nowrap">
                                    <SelectValue placeholder="Select category" />
                                 </SelectTrigger>
                                 <SelectContent className="[&_*[role=option]>span]:end-2 [&_*[role=option]>span]:start-auto [&_*[role=option]]:pe-8 [&_*[role=option]]:ps-2">
                                    {formSchema.shape.category.options.map(
                                       (category) => (
                                          <SelectItem
                                             key={category}
                                             value={category}
                                          >
                                             {category}
                                          </SelectItem>
                                       ),
                                    )}
                                 </SelectContent>
                              </Select>
                           </FormControl>
                           <FormMessage />
                        </FormItem>
                     )}
                  />
                  <FormField
                     control={form.control}
                     name="trailerLink"
                     render={({ field }) => (
                        <FormItem>
                           <FormLabel>Trailer Link</FormLabel>
                           <FormControl>
                              <Input placeholder="Trailer Link" {...field} />
                           </FormControl>
                           <FormMessage />
                        </FormItem>
                     )}
                  />
                  <FormField
                     control={form.control}
                     name="genres"
                     render={({ field }) => (
                        <FormItem>
                           <FormLabel>Genres</FormLabel>
                           <FormControl>
                              <MultipleSelector
                                 {...field}
                                 defaultOptions={genres}
                                 placeholder="Select genres"
                                 hidePlaceholderWhenSelected
                                 emptyIndicator={
                                    <p className="text-center text-sm">
                                       No results found
                                    </p>
                                 }
                              />
                           </FormControl>
                           <FormMessage />
                        </FormItem>
                     )}
                  />
                  <FormField
                     control={form.control}
                     name="poster"
                     render={({ field }) => (
                        <FormItem>
                           <FormLabel>Series Image</FormLabel>
                           <FormControl>
                              <Input
                                 type="file"
                                 accept="image/*"
                                 placeholder="Series Image"
                                 onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) {
                                       field.onChange(file);
                                       setImage(file);
                                    }
                                 }}
                              />
                           </FormControl>
                           <FormMessage />
                        </FormItem>
                     )}
                  />

                  {image && (
                     <div className="relative aspect-square h-20 overflow-hidden rounded-xl">
                        <Image
                           className="object-cover"
                           src={
                              typeof image === "string"
                                 ? image
                                 : URL.createObjectURL(image as Blob)
                           }
                           alt={""}
                           fill
                        />
                     </div>
                  )}

                  <EpisodesTable />

                  {error.message && (
                     <p className="text-sm font-medium text-destructive">
                        {error.message}
                     </p>
                  )}
                  <Button type="submit" disabled={pending}>
                     {pending && <Loader2 className="animate-spin" />}
                     {pending ? "" : id ? "Update" : "Add"}
                  </Button>
               </form>
            </Form>
         </div>
      </FormWrapper>
   );
}

export default SeriesForm;

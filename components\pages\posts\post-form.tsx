"use client";

import { SimpleEditor } from "@/components/tiptap-editor/simple-editor";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
   Cropper,
   CropperCropArea,
   CropperDescription,
   CropperImage,
} from "@/components/ui/cropper";
import {
   <PERSON><PERSON>,
   DialogContent,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";
import {
   Form,
   FormControl,
   FormDescription,
   FormField,
   FormItem,
   FormLabel,
   FormMessage,
} from "@/components/ui/form";
import { FormWrapper } from "@/components/ui/form-wrapper";
import { Input } from "@/components/ui/input";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { TagInput } from "@/components/ui/tag-input";
import { useAutoSave } from "@/hooks/use-auto-save";
import {
   ACCEPTED_IMAGE_TYPES,
   MAX_FILE_SIZE,
   SUGGESTED_TAGS,
} from "@/lib/constants";
import { POST_SECTIONS } from "@/lib/constants/post-sections";
import { createDraft } from "@/lib/firebase/drafts/actions";
import { createPost, updatePost } from "@/lib/firebase/posts/actions";
import { getPostById } from "@/lib/firebase/posts/service";
import { Author, Category } from "@/lib/firebase/types";
import { formatBytes } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import {
   BookOpenText,
   Check,
   ImageIcon,
   Layers,
   Loader2,
   PenLine,
   Tags,
   X,
} from "lucide-react";
import NextImage from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useRef, useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

const formSchema = z.object({
   title: z.string().min(3, {
      message: "Title must be at least 3 characters.",
   }),
   summary: z.string().min(5, {
      message: "Summary must be at least 5 characters.",
   }),
   categoryId: z.string({
      required_error: "Category is required.",
   }),
   authorId: z.string({
      required_error: "Author is required.",
   }),
   poster: z.union([
      z
         .instanceof(File, {
            message: "Please select an image file.",
         })
         .refine((file) => file.size <= MAX_FILE_SIZE, {
            message: `The image is too large. Please choose an image smaller than ${formatBytes(MAX_FILE_SIZE)}.`,
         })
         .refine((file) => ACCEPTED_IMAGE_TYPES.includes(file.type), {
            message: "Please upload a valid image file (JPEG, PNG, or WebP).",
         }),
      z.string().url({
         message: "Please provide a valid URL for the image.",
      }),
   ]),
   tags: z.array(z.string()),
   content: z.string().min(10, {
      message: "Content must be at least 10 characters.",
   }),
   sections: z.array(z.string()).default([]),
});

export type PostFormType = z.infer<typeof formSchema>;

type Props = {
   categories: Category[];
   authors: Author[];
};

function PostForm({ categories, authors }: Props) {
   const searchParams = useSearchParams();
   const router = useRouter();
   const id = searchParams.get("id");

   const [pending, startTransition] = useTransition();
   const [error, setError] = useState({ message: "" });
   const [image, setImage] = useState<string | Blob>("");
   const [initialContent, setInitialContent] = useState("");
   const [dataLoaded, setDataLoaded] = useState<boolean | undefined>(
      id ? false : undefined,
   );

   // State for image cropping
   const [isCropperOpen, setIsCropperOpen] = useState(false);
   const [selectedFile, setSelectedFile] = useState<File | null>(null);
   const [cropData, setCropData] = useState<{
      x: number;
      y: number;
      width: number;
      height: number;
   } | null>(null);
   const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(
      null,
   );

   // Use a ref to track if we need to update crop data to prevent infinite loops
   const cropDataRef = useRef<{
      x: number;
      y: number;
      width: number;
      height: number;
   } | null>(null);

   // Memoized crop data handler to prevent infinite loops
   const handleCropChange = useCallback(
      (
         data: {
            x: number;
            y: number;
            width: number;
            height: number;
         } | null,
      ) => {
         // Only update state if the data has actually changed
         if (JSON.stringify(cropDataRef.current) !== JSON.stringify(data)) {
            cropDataRef.current = data;
            setCropData(data);
         }
      },
      [],
   );

   // Create a reference to the file input
   const fileInputRef = useRef<HTMLInputElement>(null);

   // Function to trigger file input click
   const handleImageContainerClick = () => {
      if (fileInputRef.current) {
         fileInputRef.current.value = "";
         fileInputRef.current.click();
      }
   };

   // Function to handle the selected file and open the cropping dialog
   const handleFileSelected = (file: File) => {
      // Validate file size
      if (file.size > MAX_FILE_SIZE) {
         toast.error(
            `File is too large. Maximum size is ${formatBytes(MAX_FILE_SIZE)}.`,
         );
         return;
      }

      // Validate file type
      if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
         toast.error("Please upload a valid image file (JPEG, PNG, or WebP).");
         return;
      }

      // Create a URL for the selected image
      const imageUrl = URL.createObjectURL(file);

      // Reset crop data first
      setCropData(null);

      // Then set the file and URL in a single batch update
      setTimeout(() => {
         setSelectedFile(file);
         setSelectedImageUrl(imageUrl);
         setIsCropperOpen(true);
      }, 0);
   };

   // Function to handle applying the cropped image
   const handleApplyCrop = () => {
      if (!cropData || !selectedFile || !selectedImageUrl) {
         console.error("Missing required data:", {
            cropData,
            selectedFile: !!selectedFile,
            selectedImageUrl: !!selectedImageUrl,
         });
         toast.error("No crop data available");
         return;
      }

      if (fileInputRef.current) {
         fileInputRef.current.value = "";
      }

      try {
         // Use FileReader to read the original file directly
         const reader = new FileReader();

         reader.onload = (event) => {
            if (!event.target?.result) {
               console.error("FileReader failed to read file");
               toast.error("Failed to read image file");
               return;
            }

            // Create an image from the file data using the global Image constructor
            const img = new window.Image();

            img.onload = () => {
               try {
                  // Create a canvas element
                  const canvas = document.createElement("canvas");
                  const ctx = canvas.getContext("2d");

                  if (!ctx) {
                     console.error("Failed to get canvas context");
                     toast.error("Could not create canvas context");
                     return;
                  }

                  // Log dimensions for debugging
                  console.log("Image dimensions:", {
                     imgWidth: img.width,
                     imgHeight: img.height,
                     cropX: cropData.x,
                     cropY: cropData.y,
                     cropWidth: cropData.width,
                     cropHeight: cropData.height,
                  });

                  // Set canvas dimensions to the cropped size
                  canvas.width = cropData.width;
                  canvas.height = cropData.height;

                  // Draw the cropped portion of the image onto the canvas
                  ctx.drawImage(
                     img,
                     cropData.x,
                     cropData.y,
                     cropData.width,
                     cropData.height,
                     0,
                     0,
                     cropData.width,
                     cropData.height,
                  );

                  // Convert canvas to blob
                  canvas.toBlob(
                     (blob) => {
                        if (!blob) {
                           console.error("Failed to create blob from canvas");
                           toast.error("Failed to create image blob");
                           return;
                        }

                        try {
                           // Create a new file from the blob
                           const croppedFile = new File(
                              [blob],
                              selectedFile.name,
                              {
                                 type: selectedFile.type,
                                 lastModified: Date.now(),
                              },
                           );

                           // Update the form field and image state
                           form.setValue("poster", croppedFile);
                           setImage(croppedFile);

                           // Clean up the object URL
                           URL.revokeObjectURL(selectedImageUrl);

                           // Clear the cropper state
                           setIsCropperOpen(false);
                           setSelectedFile(null);
                           setSelectedImageUrl(null);
                           setCropData(null);
                        } catch (error) {
                           console.error(
                              "Error creating File from blob:",
                              error,
                           );
                           toast.error("Failed to process cropped image");
                        }
                     },
                     selectedFile.type,
                     0.95, // High quality
                  );
               } catch (error) {
                  console.error("Error in canvas operations:", error);
                  toast.error("Failed to process image crop");
               }
            };

            img.onerror = () => {
               console.error("Failed to load image from data URL");
               toast.error("Failed to load image for cropping");
            };

            // Set the image source from the FileReader result
            img.src = event.target.result as string;
         };

         reader.onerror = () => {
            console.error("Error reading file:", reader.error);
            toast.error("Failed to read image file");
         };

         // Read the selected file as a data URL
         reader.readAsDataURL(selectedFile);
      } catch (error) {
         console.error("Error cropping image:", error);
         toast.error("Failed to crop image. Please try again.");
      }
   };

   // Function to cancel cropping
   const handleCancelCrop = () => {
      if (selectedImageUrl) {
         URL.revokeObjectURL(selectedImageUrl);
      }
      setIsCropperOpen(false);
      setSelectedFile(null);
      setSelectedImageUrl(null);
      setCropData(null);

      if (fileInputRef.current) {
         fileInputRef.current.value = "";
      }
   };

   // Initialize form with default values
   const form = useForm<z.infer<typeof formSchema>>({
      resolver: zodResolver(formSchema),
      defaultValues: {
         title: "",
         summary: "",
         poster: undefined,
         tags: [],
         content: "",
         sections: [],
      },
   });

   // Use a ref to track if we've already loaded saved data
   const loadedSavedDataRef = useRef(false);
   const [autoSaveInitialized, setAutoSaveInitialized] = useState(false);

   // Auto-save form data to localStorage (only for new posts)
   const { saveData, loadSavedData, clearSavedData, isReady } = useAutoSave<
      z.infer<typeof formSchema>
   >({
      key: "new-post-draft",
      delay: 2000,
      enabled: !id, // Only enable auto-save for new posts
   });

   // Initialize auto-save once the component is mounted and localStorage is available
   useEffect(() => {
      if (isReady && !id && !autoSaveInitialized) {
         setAutoSaveInitialized(true);
      }
   }, [isReady, id, autoSaveInitialized]);

   // Set up auto-save on form changes
   useEffect(() => {
      if (id) return; // Only auto-save for new posts
      if (!isReady) return; // Only proceed if localStorage is available
      if (!autoSaveInitialized) return; // Only proceed if auto-save is initialized
      if (!loadedSavedDataRef.current) return; // Wait until after initial load

      // Subscribe to form changes
      const subscription = form.watch((value) => {
         // Only save if we have some actual content
         if (value.title || value.summary || value.content) {
            try {
               saveData(value as z.infer<typeof formSchema>);
            } catch (error) {
               console.error("Error saving form data:", error);
            }
         }
      });

      // Cleanup subscription
      return () => subscription.unsubscribe();
   }, [form, id, saveData, isReady, autoSaveInitialized]);

   async function onSubmit(values: z.infer<typeof formSchema>) {
      startTransition(async () => {
         const result = id
            ? await updatePost(values, id)
            : await createPost(values);

         if (!result.success) {
            setError(result);
            toast.error(result.message);
            return;
         }

         toast.success(result.message);

         // Clear saved data from localStorage after successful submission
         if (!id && isReady) {
            try {
               clearSavedData();
            } catch (error) {
               console.error("Error clearing saved data:", error);
            }
         }

         form.reset();
         router.push("/posts");
      });
   }

   useEffect(() => {
      async function getPostData() {
         if (!id) {
            // For new posts, try to load saved data from localStorage
            if (!loadedSavedDataRef.current && isReady) {
               try {
                  const savedData = loadSavedData();
                  if (savedData) {
                     // Populate form with saved data
                     Object.entries(savedData).forEach(([key, value]) => {
                        if (value !== undefined && key !== "poster") {
                           form.setValue(
                              key as keyof z.infer<typeof formSchema>,
                              value,
                           );
                        }
                     });

                     // Set content and other state variables if they exist in saved data
                     if (savedData.content) {
                        setInitialContent(savedData.content as string);
                     }

                     toast.info("Restored your draft from your last session");
                  }
               } catch (error) {
                  console.error("Error loading saved data:", error);
               } finally {
                  loadedSavedDataRef.current = true;
               }
            }
            return;
         }

         try {
            const data = await getPostById(id);

            if (data) {
               form.setValue("title", data.title);
               form.setValue("summary", data.summary);
               form.setValue("categoryId", data.category.id);
               form.setValue("authorId", data.author.id);
               form.setValue("poster", data.poster);
               form.setValue("tags", data.tags);
               form.setValue("sections", data.sections || []);
               setImage(data.poster);
               setInitialContent(data.content);
               form.setValue("content", data.content);
               setDataLoaded(true);
            } else {
               setDataLoaded(false);
            }
         } catch (error) {
            console.error("Error loading post:", error);
            toast.error("Post not found or invalid ID");
            router.push("/posts");
         }
      }

      getPostData();
   }, [id, form, loadSavedData, router, isReady]);

   return (
      <FormWrapper isSubmitting={pending} isDataLoaded={dataLoaded}>
         <div className="container mx-auto max-w-5xl">
            <div className="mb-4 text-center">
               <h1 className="text-2xl font-bold tracking-tight">
                  {id ? "Edit Post" : "Add a New Post"}
               </h1>
            </div>

            <Form {...form}>
               <form className="space-y-6">
                  {/* Main content section */}
                  <div className="rounded-lg border border-border/50 bg-card p-6 shadow-sm">
                     <div className="mb-6 flex items-center gap-2">
                        <PenLine className="h-4 w-4 text-primary" />
                        <h2 className="text-lg font-semibold">Post Details</h2>
                     </div>
                     <div className="space-y-6">
                        <FormField
                           control={form.control}
                           name="title"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel className="text-base font-medium">
                                    Post Title
                                 </FormLabel>
                                 <FormDescription>
                                    Create a compelling title that captures
                                    attention
                                 </FormDescription>
                                 <FormControl>
                                    <Input
                                       placeholder="Enter post title here"
                                       {...field}
                                    />
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />

                        <FormField
                           control={form.control}
                           name="summary"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel className="text-base font-medium">
                                    Post Summary
                                 </FormLabel>
                                 <FormDescription>
                                    Write a brief summary that will appear in
                                    previews
                                 </FormDescription>
                                 <FormControl>
                                    <Input
                                       placeholder="Summarize post in a few sentences"
                                       {...field}
                                    />
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />
                     </div>
                  </div>

                  {/* Featured Image Section */}
                  <div className="rounded-lg border border-border/50 bg-card p-6 shadow-sm">
                     <div className="mb-6 flex items-center gap-2">
                        <ImageIcon className="h-4 w-4 text-primary" />
                        <h2 className="text-lg font-semibold">
                           Featured Image
                        </h2>
                     </div>

                     <FormField
                        control={form.control}
                        name="poster"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel className="text-base font-medium">
                                 Cover Image
                              </FormLabel>
                              <FormDescription>
                                 Upload an eye-catching image for your post
                                 (recommended size: 1200×630px)
                              </FormDescription>
                              <div className="mt-2">
                                 {image ? (
                                    <div
                                       className="relative mb-4 cursor-pointer overflow-hidden rounded-lg border-2 border-dashed border-border bg-muted/20 transition-colors hover:bg-muted/30"
                                       onClick={handleImageContainerClick}
                                    >
                                       <div className="relative aspect-video w-full overflow-hidden rounded-md">
                                          <NextImage
                                             className="object-cover"
                                             src={
                                                typeof image === "string"
                                                   ? image
                                                   : URL.createObjectURL(
                                                        image as Blob,
                                                     )
                                             }
                                             alt="Featured post image"
                                             fill
                                          />
                                       </div>
                                       <div className="p-3 text-center text-sm text-muted-foreground">
                                          Click to change the image
                                       </div>
                                    </div>
                                 ) : (
                                    <div
                                       className="flex aspect-video cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-border bg-muted/20 p-12 text-center transition-colors hover:bg-muted/30"
                                       onClick={handleImageContainerClick}
                                    >
                                       <ImageIcon className="mb-4 h-12 w-12 text-muted-foreground" />
                                       <p className="mb-2 text-sm font-medium text-muted-foreground">
                                          Click to browse for an image
                                       </p>
                                       <p className="text-xs text-muted-foreground">
                                          Supports JPEG, PNG, and WebP (max{" "}
                                          {formatBytes(MAX_FILE_SIZE)})
                                       </p>
                                    </div>
                                 )}
                              </div>
                              <FormControl>
                                 <Input
                                    ref={fileInputRef}
                                    type="file"
                                    accept="image/*"
                                    className="mt-2 hidden"
                                    onChange={(e) => {
                                       const file = e.target.files?.[0];
                                       if (file) {
                                          // Store the field reference for later use in handleApplyCrop
                                          field.onChange(file); // This is needed to satisfy the form validation
                                          handleFileSelected(file);
                                       }
                                    }}
                                 />
                              </FormControl>
                              <FormMessage />
                           </FormItem>
                        )}
                     />
                  </div>

                  {/* Content Editor Section */}
                  <div className="rounded-lg border border-border/50 bg-card p-6 shadow-sm">
                     <div className="mb-6 flex items-center gap-2">
                        <BookOpenText className="h-4 w-4 text-primary" />
                        <h2 className="text-lg font-semibold">Post Content</h2>
                     </div>

                     <FormField
                        control={form.control}
                        name="content"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel className="text-base font-medium">
                                 Write Your Story
                              </FormLabel>
                              <FormDescription>
                                 Use the editor below to craft your post with
                                 rich formatting
                              </FormDescription>
                              <div className="relative mt-2 rounded-lg border border-border/50">
                                 <FormControl>
                                    <SimpleEditor
                                       key={`editor-${id || "new"}`}
                                       onChange={(value) => {
                                          field.onChange(value);
                                       }}
                                       initialContent={initialContent}
                                    />
                                 </FormControl>
                              </div>
                              <FormMessage />
                           </FormItem>
                        )}
                     />
                  </div>

                  {/* Publishing Details Section */}
                  <div className="rounded-lg border border-border/50 bg-card p-6 shadow-sm">
                     <div className="mb-6 flex items-center gap-2">
                        <Tags className="h-4 w-4 text-primary" />
                        <h2 className="text-lg font-semibold">
                           Publishing Details
                        </h2>
                     </div>

                     <div className="grid gap-6 md:grid-cols-2">
                        <FormField
                           control={form.control}
                           name="categoryId"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel className="text-base font-medium">
                                    Category
                                 </FormLabel>
                                 <FormDescription>
                                    Choose the most relevant category for your
                                    post
                                 </FormDescription>
                                 <FormControl>
                                    <Select
                                       {...field}
                                       onValueChange={(value) =>
                                          field.onChange(value)
                                       }
                                    >
                                       <SelectTrigger className="w-full">
                                          <SelectValue placeholder="Select a category" />
                                       </SelectTrigger>
                                       <SelectContent>
                                          {categories.map((category) => (
                                             <SelectItem
                                                key={category.name}
                                                value={category.id}
                                             >
                                                {category.name}
                                             </SelectItem>
                                          ))}
                                       </SelectContent>
                                    </Select>
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />

                        <FormField
                           control={form.control}
                           name="authorId"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel className="text-base font-medium">
                                    Author
                                 </FormLabel>
                                 <FormDescription>
                                    Select the author of this post
                                 </FormDescription>
                                 <FormControl>
                                    <Select
                                       {...field}
                                       onValueChange={(value) =>
                                          field.onChange(value)
                                       }
                                    >
                                       <SelectTrigger className="w-full">
                                          <SelectValue placeholder="Select an author" />
                                       </SelectTrigger>
                                       <SelectContent>
                                          {authors.map((author) => (
                                             <SelectItem
                                                key={author.name}
                                                value={author.id}
                                             >
                                                {author.name}
                                             </SelectItem>
                                          ))}
                                       </SelectContent>
                                    </Select>
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />
                     </div>

                     {/* Tags Section */}
                     <div className="mt-6">
                        <FormField
                           control={form.control}
                           name="tags"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel className="text-base font-medium">
                                    Tags
                                 </FormLabel>
                                 <FormDescription>
                                    Add relevant tags to help categorize your
                                    post
                                 </FormDescription>
                                 <FormControl>
                                    <TagInput
                                       value={field.value}
                                       onChange={field.onChange}
                                       suggestedTags={SUGGESTED_TAGS}
                                       placeholder="Add tags..."
                                       helperText="Press Enter or Comma to add a tag"
                                    />
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />
                     </div>
                  </div>

                  {/* Post Sections */}
                  <div className="rounded-lg border border-border/50 bg-card p-6 shadow-sm">
                     <div className="mb-6 flex items-center gap-2">
                        <Layers className="h-4 w-4 text-primary" />
                        <h2 className="text-lg font-semibold">Post Sections</h2>
                     </div>

                     <FormField
                        control={form.control}
                        name="sections"
                        render={() => (
                           <FormItem>
                              <div className="mb-4">
                                 <FormLabel className="text-base font-medium">
                                    Display Sections
                                 </FormLabel>
                                 <FormDescription>
                                    Select which sections this post should
                                    appear in
                                 </FormDescription>
                              </div>
                              <div className="grid gap-4 md:grid-cols-3">
                                 {POST_SECTIONS.map((section) => (
                                    <FormField
                                       key={section.id}
                                       control={form.control}
                                       name="sections"
                                       render={({ field }) => {
                                          return (
                                             <FormItem
                                                key={section.id}
                                                className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4"
                                             >
                                                <FormControl>
                                                   <Checkbox
                                                      checked={field.value?.includes(
                                                         section.id,
                                                      )}
                                                      onCheckedChange={(
                                                         checked,
                                                      ) => {
                                                         return checked
                                                            ? field.onChange([
                                                                 ...field.value,
                                                                 section.id,
                                                              ])
                                                            : field.onChange(
                                                                 field.value?.filter(
                                                                    (value) =>
                                                                       value !==
                                                                       section.id,
                                                                 ),
                                                              );
                                                      }}
                                                   />
                                                </FormControl>
                                                <div className="space-y-1 leading-none">
                                                   <FormLabel>
                                                      {section.name}
                                                   </FormLabel>
                                                   <FormDescription>
                                                      {section.description}
                                                   </FormDescription>
                                                </div>
                                             </FormItem>
                                          );
                                       }}
                                    />
                                 ))}
                              </div>
                              <FormMessage />
                           </FormItem>
                        )}
                     />
                  </div>

                  {error.message && (
                     <div className="rounded-lg border border-destructive/30 bg-destructive/10 p-4 text-destructive">
                        <p className="text-sm font-medium">{error.message}</p>
                     </div>
                  )}

                  <div className="flex justify-end space-x-4">
                     <Button
                        variant="outline"
                        type="button"
                        disabled={pending}
                        onClick={() => {
                           startTransition(async () => {
                              const values = form.getValues();
                              const result = await createDraft(values);

                              if (!result.success) {
                                 setError(result);
                                 toast.error(result.message);
                                 return;
                              }

                              // Clear saved data from localStorage after saving as draft
                              if (!id && isReady) {
                                 try {
                                    clearSavedData();
                                 } catch (error) {
                                    console.error(
                                       "Error clearing saved data:",
                                       error,
                                    );
                                 }
                              }

                              toast.success(result.message);
                              form.reset();
                              router.push("/posts/drafts");
                           });
                        }}
                     >
                        {pending && (
                           <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        )}
                        Save as Draft
                     </Button>
                     <Button
                        type="submit"
                        disabled={pending}
                        onClick={form.handleSubmit(onSubmit)}
                        className="min-w-[120px]"
                     >
                        {pending && (
                           <Loader2 className="h-4 w-4 animate-spin" />
                        )}
                        {pending
                           ? "Processing..."
                           : id
                             ? "Update Post"
                             : "Publish Post"}
                     </Button>
                  </div>
               </form>
            </Form>

            {/* Image Cropping Dialog */}
            <Dialog
               open={isCropperOpen}
               onOpenChange={(open) => {
                  if (!open) {
                     handleCancelCrop();
                  }
               }}
            >
               <DialogContent className="sm:max-w-[600px]">
                  <DialogHeader>
                     <DialogTitle>Crop Image</DialogTitle>
                  </DialogHeader>

                  {selectedFile && selectedImageUrl && (
                     <div className="flex flex-col space-y-4">
                        <div className="h-[400px] w-full">
                           <Cropper
                              className="h-full"
                              image={selectedImageUrl}
                              aspectRatio={16 / 9}
                              onCropChange={handleCropChange}
                              minZoom={1}
                              maxZoom={5}
                              cropPadding={20}
                           >
                              <CropperDescription>
                                 Drag to move, scroll to zoom
                              </CropperDescription>
                              <CropperImage />
                              <CropperCropArea />
                           </Cropper>
                        </div>

                        <DialogFooter className="gap-2 sm:space-x-0">
                           <Button
                              variant="outline"
                              onClick={handleCancelCrop}
                              className="gap-1"
                           >
                              <X className="h-4 w-4" />
                              Cancel
                           </Button>
                           <Button
                              onClick={handleApplyCrop}
                              className="ml-0 gap-1"
                              disabled={!cropData}
                           >
                              <Check className="h-4 w-4" />
                              Apply
                           </Button>
                        </DialogFooter>
                     </div>
                  )}
               </DialogContent>
            </Dialog>
         </div>
      </FormWrapper>
   );
}

export default PostForm;
